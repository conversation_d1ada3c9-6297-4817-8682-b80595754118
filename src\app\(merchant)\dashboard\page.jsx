import DonutChart from "@/components/charts/donut/donutChart";
import RecentTransitions from "@/components/Dashboard/RecentTransitions";
import UserCard from "@/components/Dashboard/UserCard";
import WalletsCards from "@/components/Dashboard/WalletsCards";

export const metadata = {
  title: "Dashboard | Money Chain",
  description: "View and manage your notifications",
};


function dashboardPage() {
  return (
    <>
      <div className="flex flex-col gap-6">
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-8">
            <UserCard></UserCard>
          </div>
          <div className="col-span-4">
            <DonutChart></DonutChart>
          </div>
        </div>
        <WalletsCards></WalletsCards>
        <RecentTransitions></RecentTransitions>
      </div>
    </>
  );
}

export default dashboardPage;
