import WithdrawalHistory from "@/components/withdrow/WithdrawalHistory";
import Link from "next/link";
import React from "react";

export const metadata = {
  title: "Withdrawal History | Money Chain",
  description: "View your withdrawal transaction history",
};

function HistoryPage() {
  // Tab configuration
  const tabs = [
    { name: "Withdraw", href: "/withdraw", current: false },
    { name: "Withdraw Account", href: "/withdraw-account", current: false },
    { name: "History", href: "/history", current: true },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Withdraw Funds
        </h1>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <Link
              key={tab.name}
              href={tab.href}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                tab.current
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              {tab.name}
            </Link>
          ))}
        </nav>
      </div>

      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Withdrawal History
        </h2>
        <div className="flex items-center space-x-3">
          <select className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm">
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="cancelled">Cancelled</option>
          </select>
          <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Export
          </button>
        </div>
      </div>

      <WithdrawalHistory />
    </div>
  );
}

export default HistoryPage;
