import React from "react";

export const metadata = {
  title: "Notifications | Money Chain",
  description: "View and manage your notifications",
};

function NotificationPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Notifications
        </h1>
        <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          Mark All as Read
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Recent Notifications
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Stay updated with your account activities and system updates.
          </p>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {/* Sample notification items */}
            <div className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Welcome to Money Chain!
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Your account has been successfully created. Start exploring our features.
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  2 hours ago
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2"></div>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  System Maintenance
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Scheduled maintenance will occur on Sunday from 2:00 AM to 4:00 AM UTC.
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  1 day ago
                </p>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                No more notifications to show.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default NotificationPage;
