"use client";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
import WithdrawalHistory from "./WithdrawalHistory";

function WithdrawRequest() {
  // use network
  const pathname = usePathname();
  const networkService = new NetworkService();

  // Tab configuration
  const tabs = [
    { name: "Withdraw", href: "/withdraw", current: pathname === "/withdraw" },
    { name: "Withdraw Account", href: "/withdraw-account", current: pathname === "/withdraw-account" },
    { name: "History", href: "/history", current: pathname === "/history" },
  ];

  const [walletData, setWalletData] = useState([]);
  const [selectWallet, setSelectWallet] = useState([]);
  const [withdrawAmount, setWithdrawAmount] = useState("");

  const [step, setStep] = useState(1);
  // step nav
  const nextStep = async () => {
    if (!handleValidated()) return;
    setStep((s) => Math.min(s + 1, 3));
  };
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));
  const resetSteps = () => setStep(1);

  // Fetch all wallets
  const fetchAllWallets = async () => {
    try {
      const res = await networkService.get(ApiPath.allWallets);
      if (res.status === "completed") {
        setWalletData(res.data.data);
      }
    } finally {
    }
  };

  useEffect(() => {
    fetchAllWallets();
  }, []);

  console.log("walletData", walletData);

  return (
    <>
      <div className="flex items-center justify-between mb-5">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Withdraw Funds
        </h1>
      </div>
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <Link
              key={tab.name}
              href={tab.href}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                tab.current
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              {tab.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* Conditional rendering based on pathname */}
      {pathname === "/history" ? (
        <WithdrawalHistory />
      ) : (
        <div className="grid grid-cols-12">
          <div className="col-span-12 lg:col-span-6 lg:col-start-4">
            {/* Step Indicators */}
            <div className="max-w-lg mx-auto mb-6">
              <div className="flex justify-between">
                {[1, 2, 3].map((s) => (
                  <div key={s} className={`step-${s}`}>
                    <div className="flex flex-col items-center">
                      <div
                        className={`step-number w-8 h-8 flex justify-center items-center rounded-full 
                      ${step === s ? "bg-blue-600 text-white" : "bg-gray-200"}`}
                      >
                        {s}
                      </div>
                      <div className="step-name">
                        {s === 1 ? "Amount" : s === 2 ? "Review" : "Success"}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {step === 1 && (
              <div className="space-y-6">
                {/* Withdraw Form */}
                <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                      Withdrawal Request
                    </h2>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                      Request a withdrawal from your available balance.
                    </p>
                  </div>
                  <div className="p-6">
                    <form className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Wallet
                        </label>
                        <select className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                          <option>Select a wallet</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Amount
                        </label>
                        <input
                          type="number"
                          placeholder="0.00"
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <button
                        type="submit"
                        className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        Request Withdrawal
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}

export default WithdrawRequest;