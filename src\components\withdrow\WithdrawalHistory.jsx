import React from "react";

function WithdrawalHistory() {
  // Sample withdrawal data - in real app this would come from API
  const withdrawals = [
    {
      id: 1,
      amount: "500.00",
      currency: "USD",
      status: "completed",
      date: "2024-01-15",
      time: "14:30",
      destination: "Bank Account ****1234",
      transactionId: "WD001234567890",
      fee: "5.00"
    },
    {
      id: 2,
      amount: "0.05",
      currency: "BTC",
      status: "pending",
      date: "2024-01-14",
      time: "09:15",
      destination: "**********************************",
      transactionId: "WD001234567891",
      fee: "0.0001"
    },
    {
      id: 3,
      amount: "1000.00",
      currency: "USD",
      status: "failed",
      date: "2024-01-13",
      time: "16:45",
      destination: "Bank Account ****5678",
      transactionId: "WD001234567892",
      fee: "10.00"
    }
  ];

  const getStatusBadge = (status) => {
    const statusClasses = {
      completed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      failed: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      cancelled: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <>
      {/* Withdrawal History */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Withdrawal History
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Track all your withdrawal requests and their status.
          </p>
        </div>
        <div className="overflow-x-auto">
          {withdrawals.length > 0 ? (
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Transaction
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Destination
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {withdrawals.map((withdrawal) => (
                  <tr key={withdrawal.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {withdrawal.transactionId}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          Fee: {withdrawal.fee} {withdrawal.currency}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {withdrawal.amount} {withdrawal.currency}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white max-w-xs truncate">
                        {withdrawal.destination}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(withdrawal.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {withdrawal.date}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {withdrawal.time}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                        View
                      </button>
                      {withdrawal.status === 'pending' && (
                        <button className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                          Cancel
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="p-6">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  No withdrawal history found.
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                  Your withdrawal requests will appear here.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default WithdrawalHistory;
